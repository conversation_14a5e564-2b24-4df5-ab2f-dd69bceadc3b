import httpx
from httpx_sse import connect_sse
from typing import Any, AsyncIterable
import logging
from common.types import (
    AgentCard,
    GetTaskRequest,
    SendTaskRequest,
    SendTaskResponse,
    JSONRPCRequest,
    GetTaskResponse,
    CancelTaskResponse,
    CancelTaskRequest,
    SetTaskPushNotificationRequest,
    SetTaskPushNotificationResponse,
    GetTaskPushNotificationRequest,
    GetTaskPushNotificationResponse,
    A2AClientHTTPError,
    A2AClientJSONError,
    SendTaskStreamingRequest,
    SendTaskStreamingResponse,
)
import json

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class A2AClient:
    def __init__(self, agent_card: AgentCard = None, url: str = None):
        if agent_card:
            self.url = agent_card.url
            logger.info(f"初始化客户端，使用Agent卡片URL: {self.url}")
        elif url:
            self.url = url
            logger.info(f"初始化客户端，使用指定URL: {self.url}")
        else:
            raise ValueError("必须提供 agent_card 或 url")

    async def send_task(self, payload: dict[str, Any]) -> SendTaskResponse:
        logger.info(f"发送任务请求，payload: {json.dumps(payload, ensure_ascii=False)}")
        request = SendTaskRequest(params=payload)
        response = await self._send_request(request)
        logger.info(f"收到任务响应: {json.dumps(response, ensure_ascii=False)}")
        return SendTaskResponse(**response)

    async def send_task_streaming(
        self, payload: dict[str, Any]
    ) -> AsyncIterable[SendTaskStreamingResponse]:
        logger.info(f"发送流式任务请求，payload: {json.dumps(payload, ensure_ascii=False)}")
        request = SendTaskStreamingRequest(params=payload)
        with httpx.Client(timeout=None) as client:
            logger.info(f"开始建立SSE连接: {self.url}")
            with connect_sse(
                client, "POST", self.url, json=request.model_dump()
            ) as event_source:
                try:
                    logger.info("开始接收SSE事件流...")
                    for sse in event_source.iter_sse():
                        logger.info(f"收到SSE事件: {sse.data}")
                        yield SendTaskStreamingResponse(**json.loads(sse.data))
                except json.JSONDecodeError as e:
                    logger.error(f"JSON解析错误: {str(e)}")
                    raise A2AClientJSONError(str(e)) from e
                except httpx.RequestError as e:
                    logger.error(f"HTTP请求错误: {str(e)}")
                    raise A2AClientHTTPError(400, str(e)) from e

    async def _send_request(self, request: JSONRPCRequest) -> dict[str, Any]:
        logger.info(f"发送HTTP请求: {self.url}")
        logger.info(f"请求内容: {json.dumps(request.model_dump(), ensure_ascii=False)}")
        async with httpx.AsyncClient() as client:
            try:
                # Image generation could take time, adding timeout
                response = await client.post(
                    self.url, json=request.model_dump(), timeout=30
                )
                response.raise_for_status()
                result = response.json()
                logger.info(f"收到HTTP响应: {json.dumps(result, ensure_ascii=False)}")
                return result
            except httpx.HTTPStatusError as e:
                logger.error(f"HTTP状态错误: {str(e)}, 状态码: {e.response.status_code}")
                raise A2AClientHTTPError(e.response.status_code, str(e)) from e
            except json.JSONDecodeError as e:
                logger.error(f"响应JSON解析错误: {str(e)}")
                raise A2AClientJSONError(str(e)) from e

    async def get_task(self, payload: dict[str, Any]) -> GetTaskResponse:
        logger.info(f"获取任务状态，payload: {json.dumps(payload, ensure_ascii=False)}")
        request = GetTaskRequest(params=payload)
        response = await self._send_request(request)
        logger.info(f"收到任务状态响应: {json.dumps(response, ensure_ascii=False)}")
        return GetTaskResponse(**response)

    async def cancel_task(self, payload: dict[str, Any]) -> CancelTaskResponse:
        logger.info(f"取消任务，payload: {json.dumps(payload, ensure_ascii=False)}")
        request = CancelTaskRequest(params=payload)
        response = await self._send_request(request)
        logger.info(f"收到取消任务响应: {json.dumps(response, ensure_ascii=False)}")
        return CancelTaskResponse(**response)

    async def set_task_callback(
        self, payload: dict[str, Any]
    ) -> SetTaskPushNotificationResponse:
        logger.info(f"设置任务回调，payload: {json.dumps(payload, ensure_ascii=False)}")
        request = SetTaskPushNotificationRequest(params=payload)
        response = await self._send_request(request)
        logger.info(f"收到设置回调响应: {json.dumps(response, ensure_ascii=False)}")
        return SetTaskPushNotificationResponse(**response)

    async def get_task_callback(
        self, payload: dict[str, Any]
    ) -> GetTaskPushNotificationResponse:
        logger.info(f"获取任务回调设置，payload: {json.dumps(payload, ensure_ascii=False)}")
        request = GetTaskPushNotificationRequest(params=payload)
        response = await self._send_request(request)
        logger.info(f"收到获取回调设置响应: {json.dumps(response, ensure_ascii=False)}")
        return GetTaskPushNotificationResponse(**response)
