import os
import logging
import json
import uuid
from typing import Dict, Any, List
import httpx
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from common.types import (
    AgentCard, AgentSkill, Task, TaskStatus, TaskState, Message,
    GetTaskResponse, SendTaskResponse, MessagePart
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ClientAgent:
    def __init__(self):
        logger.info("初始化 ClientAgent...")
        # 初始化已知的智能体信息
        self.known_agents = {
            "python_agent": AgentCard(
                name="Python Agent",
                description="用于处理 Python 相关任务的智能体",
                version="1.0.0",
                url="http://localhost:5000",
                capabilities={
                    "streaming": True,
                    "pushNotifications": False,
                    "stateTransitionHistory": True
                },
                skills=[
                    AgentSkill(
                        id="python_dev",
                        name="Python Development",
                        description="能够进行 Python 开发任务"
                    )
                ]
            ),
            "frontend_agent": Agent<PERSON><PERSON>(
                name="Frontend Agent",
                description="用于处理前端开发任务的智能体",
                version="1.0.0",
                url="http://localhost:5001",
                capabilities={
                    "streaming": True,
                    "pushNotifications": False,
                    "stateTransitionHistory": True
                },
                skills=[
                    AgentSkill(
                        id="frontend_dev",
                        name="Frontend Development",
                        description="能够进行前端开发任务"
                    )
                ]
            )
        }
        self.active_tasks = {}  # 存储活动任务
        logger.info(f"ClientAgent 初始化完成，已知智能体: {list(self.known_agents.keys())}")
        
    async def discover_agents(self):
        """发现可用的智能体"""
        # 这里应该实现智能体发现机制
        # 例如：通过服务发现、注册中心等
        pass
        
    async def select_agent(self, task_description: str) -> str:
        """根据任务描述选择合适的智能体"""
        logger.info(f"开始分析任务描述: {task_description}")
        
        # 分析任务需求
        if "前端" in task_description.lower() or "react" in task_description.lower() or "vue" in task_description.lower():
            logger.info("检测到前端相关任务，选择 frontend_agent")
            return "frontend_agent"
        elif "后端" in task_description.lower() or "python" in task_description.lower() or "fastapi" in task_description.lower():
            logger.info("检测到后端相关任务，选择 python_agent")
            return "python_agent"
        else:
            # 默认选择 python_agent
            logger.info("未检测到明确的开发类型，默认选择 python_agent")
            return "python_agent"
        
    async def create_task(self, description: str) -> Task:
        """创建新任务"""
        task_id = str(uuid.uuid4())
        task = Task(
            id=task_id,
            sessionId=str(uuid.uuid4()),
            status=TaskStatus(state=TaskState.SUBMITTED),
            history=[Message(role="system", parts=[MessagePart(type="text", text="任务已创建")])],
            description=description
        )
        self.active_tasks[task_id] = task
        return task
        
    async def assign_task(self, task: Task, agent_id: str):
        """将任务分配给指定的智能体"""
        if agent_id not in self.known_agents:
            raise ValueError(f"未知的智能体: {agent_id}")
            
        agent = self.known_agents[agent_id]
        logger.info(f"正在分配任务到智能体 {agent_id}，URL: {agent.url}")
        
        # 创建带有更长超时时间的客户端
        async with httpx.AsyncClient(timeout=60.0) as client:
            try:
                # 首先检查智能体是否在线
                try:
                    health_check = await client.get(f"{agent.url}/.well-known/agent.json", timeout=5.0)
                    if health_check.status_code != 200:
                        raise HTTPException(status_code=500, detail=f"智能体 {agent_id} 不可用")
                    logger.info(f"智能体 {agent_id} 在线")
                except Exception as e:
                    logger.error(f"智能体健康检查失败: {str(e)}")
                    raise HTTPException(status_code=500, detail=f"智能体 {agent_id} 不可用: {str(e)}")
                
                # 构建符合 A2A 协议的请求
                request_data = {
                    "jsonrpc": "2.0",
                    "id": str(uuid.uuid4()),
                    "method": "tasks/sendSubscribe",
                    "params": {
                        "id": task.id,
                        "sessionId": task.sessionId,
                        "message": {
                            "role": "user",
                            "parts": [{"type": "text", "text": task.description}]
                        }
                    }
                }
                
                logger.info(f"发送任务请求到智能体: {json.dumps(request_data, ensure_ascii=False)}")
                
                # 使用流式响应
                async with client.stream(
                    "POST",
                    f"{agent.url}/",
                    json=request_data,
                    timeout=60.0
                ) as response:
                    logger.info(f"收到响应状态码: {response.status_code}")
                    if response.status_code != 200:
                        error_msg = f"任务分配失败: {await response.text()}"
                        logger.error(error_msg)
                        raise HTTPException(status_code=500, detail=error_msg)
                    
                    collected_response = ""
                    logger.info("开始接收流式响应...")
                    
                    # 设置响应头以支持流式传输
                    response.headers["Content-Type"] = "text/event-stream"
                    response.headers["Cache-Control"] = "no-cache"
                    response.headers["Connection"] = "keep-alive"
                    
                    async for line in response.aiter_lines():
                        logger.info(f"收到原始行: {line}")
                        if not line.strip():
                            continue
                            
                        if line.startswith("data: "):
                            data = line[6:]
                            logger.info(f"处理数据: {data}")
                            if data == "[DONE]":
                                logger.info("收到完成标记")
                                break
                            try:
                                chunk = json.loads(data)
                                logger.info(f"解析的JSON数据: {json.dumps(chunk, ensure_ascii=False)}")
                                if chunk.get("result"):
                                    result = chunk["result"]
                                    if isinstance(result, dict):
                                        # 修复 JSON 数据中的字段
                                        if "id" in result and len(result["id"]) < 36:
                                            result["id"] = task.id
                                        if "sessionId" in result and len(result["sessionId"]) < 36:
                                            result["sessionId"] = task.sessionId
                                            
                                        collected_response = json.dumps(result)
                                        logger.info(f"更新收集的响应: {collected_response}")
                                        # 更新任务状态
                                        if "status" in result:
                                            task.status = result["status"]
                                            self.active_tasks[task.id] = task
                                            logger.info(f"更新任务状态: {json.dumps(result['status'], ensure_ascii=False)}")
                                        # 如果是最终结果，调用前端 Agent
                                        if result.get("status", {}).get("state") == TaskState.COMPLETED:
                                            logger.info("Python Agent 任务完成，开始调用前端 Agent")
                                            # 调用前端 Agent
                                            frontend_result = await self._call_frontend_agent(task, result)
                                            if frontend_result:
                                                result["frontend"] = frontend_result
                                            logger.info("任务处理完成，返回结果")
                                            return result
                            except json.JSONDecodeError as e:
                                logger.error(f"JSON解析错误: {str(e)}, 原始数据: {data}")
                                continue
                    
                    logger.info(f"流式响应结束，最终收集的响应: {collected_response}")
                    # 如果没有收到完成状态，返回当前收集的响应
                    if collected_response:
                        try:
                            return json.loads(collected_response)
                        except json.JSONDecodeError as e:
                            logger.error(f"最终响应JSON解析错误: {str(e)}")
                            return {"status": "error", "message": "响应解析失败"}
                    return {"status": "unknown"}
                
            except httpx.TimeoutException:
                error_msg = "连接智能体超时，请检查智能体服务是否正常运行"
                logger.error(error_msg)
                raise HTTPException(status_code=500, detail=error_msg)
            except httpx.RequestError as e:
                error_msg = f"连接智能体失败: {str(e)}"
                logger.error(error_msg)
                raise HTTPException(status_code=500, detail=error_msg)
            except Exception as e:
                error_msg = f"任务分配过程中发生错误: {str(e)}"
                logger.error(error_msg)
                raise HTTPException(status_code=500, detail=error_msg)
                
    async def _call_frontend_agent(self, task: Task, python_result: dict) -> dict:
        """调用前端 Agent 处理任务"""
        frontend_agent = self.known_agents.get("frontend_agent")
        if not frontend_agent:
            logger.error("前端 Agent 未找到")
            return None
            
        logger.info("开始调用前端 Agent...")
        async with httpx.AsyncClient(timeout=60.0) as client:
            try:
                # 构建前端 Agent 请求
                request_data = {
                    "jsonrpc": "2.0",
                    "id": str(uuid.uuid4()),
                    "method": "tasks/sendSubscribe",
                    "params": {
                        "id": task.id,
                        "sessionId": task.sessionId,
                        "message": {
                            "role": "user",
                            "parts": [
                                {"type": "text", "text": task.description},
                                {"type": "text", "text": f"后端代码已生成: {json.dumps(python_result, ensure_ascii=False)}"}
                            ]
                        }
                    }
                }
                
                logger.info(f"发送请求到前端 Agent: {json.dumps(request_data, ensure_ascii=False)}")
                
                async with client.stream(
                    "POST",
                    f"{frontend_agent.url}/",
                    json=request_data,
                    timeout=60.0
                ) as response:
                    if response.status_code != 200:
                        logger.error(f"前端 Agent 请求失败: {await response.text()}")
                        return None
                        
                    collected_response = ""
                    logger.info("开始接收前端 Agent 的流式响应...")
                    
                    async for line in response.aiter_lines():
                        if not line.strip():
                            continue
                            
                        if line.startswith("data: "):
                            data = line[6:]
                            if data == "[DONE]":
                                logger.info("前端 Agent 响应完成")
                                break
                            try:
                                chunk = json.loads(data)
                                if chunk.get("result"):
                                    result = chunk["result"]
                                    if isinstance(result, dict):
                                        collected_response = json.dumps(result)
                                        logger.info(f"前端 Agent 响应更新: {json.dumps(result, ensure_ascii=False)}")
                                        if result.get("status", {}).get("state") == TaskState.COMPLETED:
                                            logger.info("前端 Agent 任务完成")
                                            return result
                            except json.JSONDecodeError as e:
                                logger.error(f"前端 Agent 响应解析错误: {str(e)}")
                                continue
                                
                    if collected_response:
                        try:
                            return json.loads(collected_response)
                        except json.JSONDecodeError as e:
                            logger.error(f"前端 Agent 最终响应解析错误: {str(e)}")
                            return None
                    return None
                    
            except Exception as e:
                logger.error(f"调用前端 Agent 时发生错误: {str(e)}")
                return None
            
    async def monitor_task(self, task_id: str):
        """监控任务状态"""
        if task_id not in self.active_tasks:
            raise ValueError(f"未知的任务: {task_id}")
            
        task = self.active_tasks[task_id]
        # 实现任务状态监控逻辑
        pass
        
    async def get_task_status(self, task_id: str) -> TaskStatus:
        """获取任务状态"""
        if task_id not in self.active_tasks:
            raise ValueError(f"未知的任务: {task_id}")
            
        return self.active_tasks[task_id].status
        
    async def cancel_task(self, task_id: str):
        """取消任务"""
        if task_id not in self.active_tasks:
            raise ValueError(f"未知的任务: {task_id}")
            
        task = self.active_tasks[task_id]
        # 实现任务取消逻辑
        pass

class TaskRequest(BaseModel):
    description: str
    requirements: Dict[str, Any] = {}

    def __str__(self):
        return f"TaskRequest(description='{self.description}')"

class TaskResponse(BaseModel):
    task_id: str
    status: str
    result: Dict[str, Any]

app = FastAPI(title="A2A Client Agent")

# 创建全局的 ClientAgent 实例
client_agent = ClientAgent()
logger.info("全局 ClientAgent 实例已创建")

@app.post("/task", response_model=TaskResponse)
async def create_and_assign_task(request: TaskRequest):
    try:
        logger.info(f"收到任务请求: {request}")
        logger.info(f"当前已知智能体: {list(client_agent.known_agents.keys())}")
        
        # 创建任务
        task = await client_agent.create_task(request.description)
        logger.info(f"任务已创建: {task.id}")
        
        # 自动选择合适的智能体
        agent_id = await client_agent.select_agent(request.description)
        logger.info(f"选择的智能体 ID: {agent_id}")
        
        # 检查智能体是否存在
        if agent_id not in client_agent.known_agents:
            logger.error(f"未知的智能体 ID: {agent_id}")
            logger.info(f"已知的智能体: {list(client_agent.known_agents.keys())}")
            raise HTTPException(status_code=400, detail=f"未知的智能体: {agent_id}")
            
        # 分配任务
        try:
            result = await client_agent.assign_task(task, agent_id)
            logger.info(f"任务处理完成，返回结果")
            return TaskResponse(
                task_id=task.id,
                status="completed",
                result=result
            )
        except ValueError as ve:
            logger.error(f"任务分配失败: {str(ve)}")
            raise HTTPException(status_code=400, detail=str(ve))
        
    except Exception as e:
        logger.error(f"处理任务时发生错误: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/task/{task_id}", response_model=TaskResponse)
async def get_task_status(task_id: str):
    try:
        status = await client_agent.get_task_status(task_id)
        return TaskResponse(
            task_id=task_id,
            status=status.state,
            result={"status": status}
        )
    except Exception as e:
        logger.error(f"获取任务状态时发生错误: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/task/{task_id}")
async def cancel_task(task_id: str):
    try:
        await client_agent.cancel_task(task_id)
        return {"status": "cancelled"}
    except Exception as e:
        logger.error(f"取消任务时发生错误: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    logger.info("启动 Client Agent 服务...")
    uvicorn.run(app, host="0.0.0.0", port=8000) 