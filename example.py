from server.server import <PERSON><PERSON><PERSON>r
from server.task_manager import TaskManager
from common.types import <PERSON><PERSON><PERSON>, AgentSkill, GetTaskResponse, CancelTaskResponse, SendTaskResponse, GetTaskPushNotificationResponse, SetTaskPushNotificationResponse, SendTaskStreamingResponse, JSONRPCResponse, Task, TaskStatus, TaskState, Message
import asyncio
import httpx
import json

# 创建一个简单的任务管理器
class SimpleTaskManager(TaskManager):
    async def on_get_task(self, request):
        task = Task(
            id=request.params.id,
            sessionId=request.params.sessionId,
            status=TaskStatus(state=TaskState.SUBMITTED),
            history=[Message(role="system", parts=[{"type": "text", "text": "Task is ready"}])]
        )
        return GetTaskResponse(id=request.id, result=task)

    async def on_send_task(self, request):
        task = Task(
            id=request.params.id,
            sessionId=request.params.sessionId,
            status=TaskStatus(state=TaskState.SUBMITTED),
            history=[request.params.message]
        )
        return SendTaskResponse(id=request.id, result=task)

    async def on_cancel_task(self, request):
        return CancelTaskResponse(id=request.id, result={"status": "cancelled", "message": "Task cancelled"})

    async def on_get_task_push_notification(self, request):
        return GetTaskPushNotificationResponse(id=request.id, result={"enabled": False})

    async def on_set_task_push_notification(self, request):
        return SetTaskPushNotificationResponse(id=request.id, result={"enabled": True})

    async def on_send_task_subscribe(self, request):
        async def generate_responses():
            yield SendTaskStreamingResponse(id=request.id, result={"status": "processing", "message": "Task is being processed"})
            yield SendTaskStreamingResponse(id=request.id, result={"status": "completed", "message": "Task completed"})
        return generate_responses()

    async def on_resubscribe_to_task(self, request):
        async def generate_responses():
            yield SendTaskResponse(id=request.id, result={"status": "resubscribed", "message": "Task resubscribed"})
        return generate_responses()

# 创建 Agent 卡片
agent_card = AgentCard(
    name="Simple Agent",
    description="A simple A2A agent",
    version="1.0.0",
    url="http://localhost:5000",
    capabilities={
        "text": True,
        "image": False,
        "audio": False,
        "video": False
    },
    skills=[
        AgentSkill(
            id="task_handling",
            name="Task Handling",
            description="Handle simple tasks"
        ),
        AgentSkill(
            id="streaming",
            name="Streaming",
            description="Support streaming responses"
        )
    ]
)

# 创建服务器
server = A2AServer(
    host="0.0.0.0",
    port=5000,
    agent_card=agent_card,
    task_manager=SimpleTaskManager()
)

async def run_client():
    async with httpx.AsyncClient() as client:
        # 发送一个符合协议的任务
        response = await client.post(
            "http://localhost:5000/",
            json={
                "jsonrpc": "2.0",
                "method": "tasks/send",
                "params": {
                    "id": "test-task-1",
                    "sessionId": "test-session-1",
                    "message": {
                        "role": "user",
                        "parts": [
                            {
                                "type": "text",
                                "text": "Hello World"
                            }
                        ]
                    }
                },
                "id": 1
            }
        )
        print("Client response:", response.json())

async def main():
    # 启动服务器
    server_task = asyncio.create_task(server.start_async())
    
    # 等待服务器启动
    await asyncio.sleep(2)
    
    # 运行客户端
    await run_client()
    
    # 等待服务器关闭
    server_task.cancel()
    try:
        await server_task
    except asyncio.CancelledError:
        pass

if __name__ == "__main__":
    asyncio.run(main()) 