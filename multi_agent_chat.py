#!/usr/bin/env python3
"""
多智能体协作终端交互
展示真正的多代理协作能力
"""
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from adk_agents import python_agent, frontend_agent
from adk_agents.tools import generate_python_code, generate_frontend_code, analyze_code_requirements


class MultiAgentOrchestrator:
    """多智能体协调器"""
    
    def __init__(self):
        self.agents = {
            "python": {
                "agent": python_agent,
                "generate_func": generate_python_code,
                "specialty": "Python后端开发、API设计、数据处理、算法实现"
            },
            "frontend": {
                "agent": frontend_agent,
                "generate_func": generate_frontend_code,
                "specialty": "前端界面、React/Vue组件、用户交互、响应式设计"
            }
        }
        self.conversation_history = []
    
    async def analyze_and_route(self, user_input):
        """分析需求并路由到合适的代理"""
        analysis = analyze_code_requirements(user_input)
        
        print(f"\n🧠 智能分析结果:")
        print(f"   建议代理: {analysis['suggested_type']}")
        print(f"   置信度: {analysis['confidence']:.2f}")
        print(f"   分析: {analysis['analysis']}")
        
        return analysis
    
    async def multi_agent_collaboration(self, user_input):
        """多代理协作处理复杂需求"""
        print(f"\n🤝 启动多代理协作模式...")
        
        # 检查是否需要多个代理
        keywords = user_input.lower()
        needs_backend = any(word in keywords for word in ['后端', 'api', 'database', '数据库', 'server', '服务器'])
        needs_frontend = any(word in keywords for word in ['前端', 'ui', 'interface', '界面', 'react', 'vue'])
        
        if needs_backend and needs_frontend:
            print("🎯 检测到全栈需求，启动前后端协作...")
            
            # 后端代理先工作
            print(f"\n👨‍💻 {self.agents['python']['agent'].name} 开始设计后端架构...")
            backend_prompt = f"为以下需求设计后端架构和API：{user_input}"
            await self._stream_generate("python", backend_prompt)
            
            print(f"\n\n🎨 {self.agents['frontend']['agent'].name} 开始设计前端界面...")
            frontend_prompt = f"基于后端API，为以下需求设计前端界面：{user_input}"
            await self._stream_generate("frontend", frontend_prompt)
            
            print(f"\n🔗 协作完成！前后端代理已为您提供完整的解决方案。")
            
        else:
            # 单代理处理
            analysis = await self.analyze_and_route(user_input)
            agent_type = analysis['suggested_type']
            
            if agent_type in self.agents:
                await self._stream_generate(agent_type, user_input)
            else:
                print("🤔 需求比较通用，让我为您推荐最合适的代理...")
                # 默认使用Python代理
                await self._stream_generate("python", user_input)
    
    async def _stream_generate(self, agent_type, prompt):
        """流式生成代码"""
        if agent_type not in self.agents:
            print(f"❌ 未知的代理类型: {agent_type}")
            return
        
        agent_info = self.agents[agent_type]
        agent = agent_info["agent"]
        
        print(f"\n💭 {agent.name} 正在思考...")
        print(f"🔧 专长: {agent_info['specialty']}")
        print(f"\n生成的代码:\n{'-'*50}")
        
        try:
            # 使用流式生成
            from adk_agents.tools.deepseek_client import deepseek_client
            
            # 根据代理类型选择系统提示
            if agent_type == "python":
                system_prompt = """你是一个专业的Python后端开发专家。请生成高质量的Python代码，包含详细注释。"""
            else:
                system_prompt = """你是一个专业的前端开发专家。请生成现代化的前端代码，注重用户体验。"""
            
            collected_text = ""
            async for chunk in deepseek_client.generate_code(prompt, system_prompt):
                if chunk:
                    print(chunk, end='', flush=True)
                    collected_text += chunk
            
            print(f"\n{'-'*50}")
            print(f"✅ {agent.name} 完成任务!")
            
            # 记录到对话历史
            self.conversation_history.append({
                "agent": agent_type,
                "prompt": prompt,
                "response": collected_text
            })
            
        except Exception as e:
            print(f"\n❌ {agent.name} 遇到错误: {str(e)}")
    
    def show_agent_status(self):
        """显示代理状态"""
        print(f"\n🤖 当前可用代理:")
        for agent_type, info in self.agents.items():
            agent = info["agent"]
            print(f"   • {agent.name}")
            print(f"     专长: {info['specialty']}")
            print(f"     工具数量: {len(agent.tools)}")
        print()


async def multi_agent_chat():
    """多智能体聊天主函数"""
    orchestrator = MultiAgentOrchestrator()
    
    print("🚀 多智能体协作系统")
    print("=" * 60)
    print("🎯 特色功能:")
    print("   • 智能需求分析和代理路由")
    print("   • 多代理协作处理复杂需求")
    print("   • 前后端代理协同工作")
    print("   • 实时流式代码生成")
    print()
    
    orchestrator.show_agent_status()
    
    print("💡 试试这些多代理需求:")
    print("   • '创建一个完整的用户管理系统'（前后端协作）")
    print("   • '设计一个博客平台'（全栈协作）")
    print("   • '实现一个在线商城'（多代理协作）")
    print()
    print("输入 'agents' 查看代理状态，'quit' 退出")
    print("-" * 60)
    
    while True:
        try:
            user_input = input("\n🧑‍💻 您: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 感谢使用多智能体协作系统！")
                break
            
            if user_input.lower() in ['agents', '代理']:
                orchestrator.show_agent_status()
                continue
            
            if user_input.lower() in ['help', '帮助']:
                print("\n📖 多智能体系统帮助:")
                print("• 直接描述需求，系统会自动分析并选择合适的代理")
                print("• 复杂需求会触发多代理协作模式")
                print("• 'agents' - 查看代理状态")
                print("• 'quit' - 退出系统")
                continue
            
            if not user_input:
                continue
            
            # 多代理协作处理
            await orchestrator.multi_agent_collaboration(user_input)
            
        except KeyboardInterrupt:
            print("\n\n👋 再见！")
            break
        except Exception as e:
            print(f"\n❌ 系统错误: {str(e)}")


def main():
    """主函数"""
    try:
        asyncio.run(multi_agent_chat())
    except Exception as e:
        print(f"❌ 启动失败: {str(e)}")


if __name__ == "__main__":
    main()
