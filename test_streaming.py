#!/usr/bin/env python3
"""
测试流式输出功能
"""
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from adk_agents.tools.deepseek_client import deepseek_client


async def test_streaming_output():
    """测试流式输出"""
    print("🌊 测试流式代码生成")
    print("=" * 40)
    
    requirement = "写一个简单的计算器类"
    system_prompt = """你是一个Python专家，请生成简洁清晰的代码。"""
    
    print(f"需求: {requirement}")
    print("\n正在生成代码（流式输出）:")
    print("-" * 40)
    
    try:
        collected_text = ""
        chunk_count = 0
        
        async for chunk in deepseek_client.generate_code(requirement, system_prompt):
            if chunk:
                # 实时打印每个字符块
                print(chunk, end='', flush=True)
                collected_text += chunk
                chunk_count += 1
                
                # 限制输出长度，避免测试时间过长
                if chunk_count > 100:
                    print("\n\n[输出已截断用于测试]")
                    break
        
        print(f"\n{'-'*40}")
        print(f"✅ 流式输出测试完成!")
        print(f"总共接收到 {chunk_count} 个数据块")
        print(f"总文本长度: {len(collected_text)} 字符")
        
    except Exception as e:
        print(f"\n❌ 流式输出测试失败: {str(e)}")


async def main():
    """主函数"""
    print("🚀 流式输出功能测试")
    print("=" * 50)
    
    await test_streaming_output()
    
    print("\n🎉 测试完成!")
    print("\n💡 现在您可以在终端交互中体验流式输出:")
    print("python terminal_chat.py --agent python")


if __name__ == "__main__":
    asyncio.run(main())
