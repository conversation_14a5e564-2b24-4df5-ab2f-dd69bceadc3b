#!/usr/bin/env python3
"""
快速测试编码修复
"""
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from adk_agents.tools.deepseek_client import deepseek_client


async def test_simple_generation():
    """测试简单的代码生成"""
    print("🧪 测试简单代码生成...")
    
    try:
        # 测试一个简单的需求
        requirement = "写一个Hello World函数"
        system_prompt = "你是一个Python专家，请生成简洁的代码。"
        
        print(f"需求: {requirement}")
        print("正在生成...")
        
        result = ""
        count = 0
        async for chunk in deepseek_client.generate_code(requirement, system_prompt):
            result += chunk
            count += 1
            if count > 50:  # 限制输出长度，避免卡住
                break
        
        print("✅ 生成成功!")
        print(f"生成内容长度: {len(result)} 字符")
        print("内容预览:")
        print("-" * 40)
        print(result[:200] + ("..." if len(result) > 200 else ""))
        print("-" * 40)
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")


async def main():
    """主函数"""
    print("🚀 快速编码测试")
    print("=" * 30)
    
    await test_simple_generation()
    
    print("\n🎉 测试完成!")


if __name__ == "__main__":
    asyncio.run(main())
