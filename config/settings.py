"""
ADK 配置管理模块
统一管理所有代理的配置信息
"""
import os
import json
from typing import Dict, Any, Optional
from pathlib import Path


class Settings:
    """统一配置管理类"""
    
    def __init__(self):
        self.config_file = Path(__file__).parent.parent / "config.json"
        self._config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if self.config_file.exists():
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            # 创建默认配置
            default_config = {
                "deepseek": {
                    "api_key": "",
                    "api_base": "https://api.deepseek.com/v1",
                    "model": "deepseek-chat"
                },
                "gemini": {
                    "api_key": "",
                    "model": "gemini-2.0-flash"
                },
                "agents": {
                    "python_agent": {
                        "name": "Python代码生成器",
                        "description": "基于ADK框架的Python代码生成助手",
                        "version": "2.0.0",
                        "port": 8001
                    },
                    "frontend_agent": {
                        "name": "前端代码生成器", 
                        "description": "基于ADK框架的前端代码生成助手",
                        "version": "2.0.0",
                        "port": 8002
                    }
                }
            }
            self._save_config(default_config)
            return default_config
    
    def _save_config(self, config: Dict[str, Any]):
        """保存配置文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
    
    def get_deepseek_config(self) -> Dict[str, Any]:
        """获取 DeepSeek 配置"""
        return self._config.get("deepseek", {})
    
    def get_gemini_config(self) -> Dict[str, Any]:
        """获取 Gemini 配置"""
        return self._config.get("gemini", {})
    
    def get_agent_config(self, agent_name: str) -> Dict[str, Any]:
        """获取指定代理的配置"""
        return self._config.get("agents", {}).get(agent_name, {})
    
    def get_api_key(self, provider: str) -> Optional[str]:
        """获取指定提供商的 API 密钥"""
        # 优先从环境变量获取
        env_key = f"{provider.upper()}_API_KEY"
        api_key = os.getenv(env_key)
        
        if api_key:
            return api_key
        
        # 从配置文件获取
        config = self._config.get(provider, {})
        return config.get("api_key")
    
    def update_config(self, section: str, key: str, value: Any):
        """更新配置"""
        if section not in self._config:
            self._config[section] = {}
        self._config[section][key] = value
        self._save_config(self._config)


# 全局配置实例
settings = Settings()
