#!/usr/bin/env python3
"""
测试 CRM 系统代码生成
验证编码问题修复
"""
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from adk_agents.tools import generate_python_code, generate_frontend_code


async def test_crm_generation():
    """测试 CRM 系统代码生成"""
    print("🏢 测试 CRM 系统代码生成")
    print("=" * 50)
    
    # 测试用例
    test_cases = [
        {
            "name": "简单 CRM 后端",
            "requirement": "创建一个简单的 CRM 系统后端，包含客户管理功能",
            "agent": "python"
        },
        {
            "name": "CRM 前端界面",
            "requirement": "创建一个 CRM 系统的客户列表页面",
            "agent": "frontend"
        },
        {
            "name": "CRM 数据模型",
            "requirement": "设计 CRM 系统的数据库模型",
            "agent": "python"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试 {i}: {test_case['name']}")
        print(f"需求: {test_case['requirement']}")
        print("-" * 40)
        
        try:
            if test_case['agent'] == 'python':
                result = await generate_python_code(test_case['requirement'])
            else:
                result = await generate_frontend_code(test_case['requirement'])
            
            if result['status'] == 'success':
                print("✅ 生成成功!")
                # 显示前 200 个字符
                code_preview = result['code'][:200]
                # 确保预览内容是安全的 UTF-8
                safe_preview = code_preview.encode('utf-8', errors='ignore').decode('utf-8')
                print(f"代码预览:\n{safe_preview}...")
                print(f"总长度: {len(result['code'])} 字符")
            else:
                print(f"❌ 生成失败: {result.get('message', '未知错误')}")
                if 'error' in result:
                    error_msg = str(result['error']).encode('utf-8', errors='ignore').decode('utf-8')
                    print(f"错误详情: {error_msg}")
                    
        except Exception as e:
            print(f"❌ 测试过程中出现异常: {str(e)}")
        
        print("-" * 40)


async def test_encoding_robustness():
    """测试编码健壮性"""
    print("\n🔧 测试编码健壮性")
    print("=" * 50)
    
    # 包含可能引起编码问题的需求
    challenging_requirements = [
        "创建一个用户管理系统，支持中文用户名",
        "实现一个包含特殊字符处理的文本编辑器",
        "设计一个多语言支持的国际化系统"
    ]
    
    for i, requirement in enumerate(challenging_requirements, 1):
        print(f"\n🧪 编码测试 {i}: {requirement}")
        
        try:
            result = await generate_python_code(requirement)
            
            if result['status'] == 'success':
                print("✅ 编码处理正常")
                # 验证返回的代码是有效的 UTF-8
                try:
                    result['code'].encode('utf-8')
                    print("✅ UTF-8 编码验证通过")
                except UnicodeEncodeError:
                    print("⚠️ UTF-8 编码验证失败，但已被处理")
            else:
                print(f"❌ 生成失败: {result.get('message', '未知错误')}")
                
        except Exception as e:
            print(f"❌ 异常: {str(e)}")


async def main():
    """主测试函数"""
    print("🚀 CRM 代码生成测试")
    print("测试编码问题修复效果")
    print("=" * 60)
    
    # 测试 CRM 系统生成
    await test_crm_generation()
    
    # 测试编码健壮性
    await test_encoding_robustness()
    
    print("\n🎉 测试完成!")
    print("\n💡 如果测试通过，您可以:")
    print("1. 重新尝试在终端中生成 CRM 系统")
    print("2. 使用 Web UI 进行更复杂的代码生成")
    print("3. 尝试其他类型的代码生成需求")


if __name__ == "__main__":
    asyncio.run(main())
