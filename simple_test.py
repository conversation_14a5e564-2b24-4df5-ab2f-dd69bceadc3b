#!/usr/bin/env python3
"""
简单的 ADK 代理测试
"""
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_agent_import():
    """测试代理导入"""
    print("📦 测试代理导入...")
    
    try:
        from adk_agents import python_agent, frontend_agent
        print(f"✅ 代理导入成功!")
        print(f"Python 代理: {python_agent.name}")
        print(f"前端代理: {frontend_agent.name}")
        print(f"Python 代理描述: {python_agent.description}")
        print(f"前端代理描述: {frontend_agent.description}")
        return True
    except Exception as e:
        print(f"❌ 代理导入失败: {str(e)}")
        return False


def test_requirement_analysis():
    """测试需求分析"""
    print("\n🔍 测试需求分析...")
    
    from adk_agents.tools import analyze_code_requirements
    
    test_cases = [
        "我需要一个 Python 爬虫来抓取网页数据",
        "创建一个 Vue.js 的用户管理界面",
        "写一个通用的数据处理函数"
    ]
    
    for requirement in test_cases:
        try:
            result = analyze_code_requirements(requirement)
            print(f"需求: {requirement}")
            print(f"分析结果: {result['analysis']}")
            print(f"建议类型: {result['suggested_type']}, 置信度: {result['confidence']:.2f}")
            print("-" * 50)
            
        except Exception as e:
            print(f"❌ 分析需求时出现异常: {str(e)}")


def test_deepseek_client():
    """测试 DeepSeek 客户端配置"""
    print("\n🔧 测试 DeepSeek 客户端配置...")
    
    try:
        from adk_agents.tools.deepseek_client import deepseek_client
        print(f"✅ DeepSeek 客户端初始化成功!")
        print(f"API Base: {deepseek_client.api_base}")
        print(f"Model: {deepseek_client.model}")
        print(f"API Key: {'已配置' if deepseek_client.api_key else '未配置'}")
        return True
    except Exception as e:
        print(f"❌ DeepSeek 客户端初始化失败: {str(e)}")
        return False


def test_adk_web_command():
    """测试 ADK Web 命令是否可用"""
    print("\n🌐 测试 ADK Web 命令...")
    
    import subprocess
    try:
        result = subprocess.run(['adk', '--help'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ ADK 命令行工具可用!")
            print("可用的命令:")
            lines = result.stdout.split('\n')
            for line in lines:
                if 'web' in line or 'run' in line or 'api_server' in line:
                    print(f"  {line.strip()}")
        else:
            print("❌ ADK 命令行工具不可用")
    except Exception as e:
        print(f"❌ 测试 ADK 命令时出现异常: {str(e)}")


def main():
    """主测试函数"""
    print("🚀 开始简单的 ADK 代理测试\n")
    
    # 测试代理导入
    if not test_agent_import():
        print("❌ 代理导入失败，停止测试")
        return
    
    print("\n" + "="*60)
    
    # 测试需求分析
    test_requirement_analysis()
    
    print("\n" + "="*60)
    
    # 测试 DeepSeek 客户端
    test_deepseek_client()
    
    print("\n" + "="*60)
    
    # 测试 ADK 命令
    test_adk_web_command()
    
    print("\n🎉 基础测试完成!")
    print("\n💡 下一步:")
    print("1. 启动 Web UI: python main.py web")
    print("2. 启动终端模式: python main.py terminal --agent python")
    print("3. 在 Web UI 中测试代码生成功能")


if __name__ == "__main__":
    main()
