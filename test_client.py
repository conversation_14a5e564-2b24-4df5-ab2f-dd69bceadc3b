import asyncio
import httpx
import json
from uuid import uuid4

async def test_agent(url: str, prompt: str):
    async with httpx.AsyncClient() as client:
        # 发送任务
        response = await client.post(
            url,
            json={
                "jsonrpc": "2.0",
                "method": "tasks/send",
                "params": {
                    "id": str(uuid4()),
                    "sessionId": str(uuid4()),
                    "message": {
                        "role": "user",
                        "parts": [
                            {
                                "type": "text",
                                "text": prompt
                            }
                        ]
                    }
                },
                "id": 1
            }
        )
        print(f"\nResponse from {url}:")
        print(json.dumps(response.json(), indent=2, ensure_ascii=False))

async def main():
    # 测试前端代码生成器
    await test_agent(
        "http://localhost:5001",
        "请帮我实现一个用户登录表单组件，使用 React 和 Ant Design，包含用户名和密码输入框，以及登录按钮。要求：\n1. 使用 TypeScript\n2. 实现表单验证\n3. 添加加载状态\n4. 实现错误提示\n5. 使用 Ant Design 的 Form 组件"
    )

if __name__ == "__main__":
    asyncio.run(main()) 