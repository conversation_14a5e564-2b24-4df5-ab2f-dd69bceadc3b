import os
from typing import List, Dict, Any, AsyncGenerator
from dotenv import load_dotenv
import httpx
import logging
import json
from server.server import A2AServer
from server.task_manager import TaskManager
from common.types import (
    AgentCard, AgentSkill, GetTaskResponse, CancelTaskResponse,
    SendTaskResponse, Task, TaskStatus, TaskState, Message
)
from .config import config

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    encoding='utf-8'
)
logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv()

class PythonCodeAgent(TaskManager):
    def __init__(self):
        # 从配置文件获取 DeepSeek API 配置
        deepseek_config = config.get_deepseek_config()
        self.api_key = deepseek_config.get("api_key")
        if not self.api_key:
            raise ValueError("请在 config.json 中设置 DeepSeek API 密钥")
        
        self.api_base = deepseek_config.get("api_base", "https://api.deepseek.com/v1")
        self.model = deepseek_config.get("model", "deepseek-chat")
        
        logger.info(f"初始化 DeepSeek API 配置: base_url={self.api_base}, model={self.model}")
        
        self.system_prompt = """你是一个专业的Python后端开发专家。你的任务是：
1. 根据用户需求生成高质量的Python代码
2. 确保代码符合PEP 8规范
3. 提供必要的注释和文档
4. 考虑代码的可维护性和可扩展性
5. 实现错误处理和日志记录
请用中文回复，并在代码前说明实现思路。"""

    async def on_get_task(self, request):
        # 创建初始任务状态
        task = Task(
            id=request.params.id,
            sessionId=request.params.sessionId,
            status=TaskStatus(state=TaskState.SUBMITTED),
            history=[Message(role="system", parts=[{"type": "text", "text": "Python代码生成器已就绪"}])]
        )
        return GetTaskResponse(id=request.id, result=task)

    async def on_send_task(self, request):
        try:
            # 获取用户需求
            user_message = request.params.message.parts[0].text
            logger.info(f"收到Python任务请求: {user_message}")
            
            async def stream_response() -> AsyncGenerator[SendTaskResponse, None]:
                async with httpx.AsyncClient(timeout=60.0) as client:
                    logger.info("正在发送流式请求到 DeepSeek API...")
                    try:
                        async with client.stream(
                            "POST",
                            f"{self.api_base}/chat/completions",
                            headers={
                                "Authorization": f"Bearer {self.api_key}",
                                "Content-Type": "application/json"
                            },
                            json={
                                "model": self.model,
                                "messages": [
                                    {"role": "system", "content": self.system_prompt},
                                    {"role": "user", "content": user_message}
                                ],
                                "temperature": 0.7,
                                "max_tokens": 2048,
                                "stream": True
                            }
                        ) as response:
                            if response.status_code != 200:
                                error_msg = f"DeepSeek API 请求失败: {await response.text()}"
                                logger.error(error_msg)
                                raise Exception(error_msg)

                            collected_text = ""
                            logger.info("开始接收 DeepSeek API 的流式响应...")
                            async for line in response.aiter_lines():
                                if line.startswith("data: "):
                                    data = line[6:]
                                    if data == "[DONE]":
                                        logger.info("DeepSeek API 响应完成")
                                        break
                                    try:
                                        chunk = json.loads(data)
                                        if chunk["choices"][0]["finish_reason"] is not None:
                                            logger.info(f"生成完成，原因: {chunk['choices'][0]['finish_reason']}")
                                            break
                                        content = chunk["choices"][0]["delta"].get("content", "")
                                        if content:
                                            # 确保内容使用 UTF-8 编码
                                            if isinstance(content, bytes):
                                                content = content.encode('utf-8').decode('utf-8')
                                            collected_text += content
                                            logger.info(f"收到新的内容片段: {content}")
                                            # 打印当前响应的 JSON 格式
                                            current_response = SendTaskResponse(
                                                id=request.id,
                                                result=Task(
                                                    id=request.params.id,
                                                    sessionId=request.params.sessionId,
                                                    status=TaskStatus(state=TaskState.WORKING),
                                                    history=[
                                                        request.params.message,
                                                        Message(role="agent", parts=[{"type": "text", "text": collected_text}])
                                                    ]
                                                )
                                            )
                                            response_json = json.dumps(current_response.dict(), ensure_ascii=False)
                                            logger.debug(f"当前响应: {response_json}")
                                            yield current_response
                                    except json.JSONDecodeError as e:
                                        logger.error(f"JSON解析错误: {str(e)}, 原始数据: {data}")
                                        continue

                            logger.info("准备发送最终完成状态...")
                            # 发送最终完成状态
                            final_response = SendTaskResponse(
                                id=request.id,
                                result=Task(
                                    id=request.params.id,
                                    sessionId=request.params.sessionId,
                                    status=TaskStatus(state=TaskState.COMPLETED),
                                    history=[
                                        request.params.message,
                                        Message(role="agent", parts=[{"type": "text", "text": collected_text}])
                                    ]
                                )
                            )
                            # 确保响应数据正确编码
                            response_json = json.dumps(final_response.dict(), ensure_ascii=False)
                            logger.debug(f"发送最终响应: {response_json}")
                            yield final_response
                            logger.info("Python代码生成任务完成")

                    except httpx.TimeoutException:
                        error_msg = "DeepSeek API 请求超时，请稍后重试"
                        logger.error(error_msg)
                        raise Exception(error_msg)
                    except httpx.RequestError as e:
                        error_msg = f"DeepSeek API 请求错误: {str(e)}"
                        logger.error(error_msg)
                        raise Exception(error_msg)

            return stream_response()

        except Exception as e:
            logger.error(f"处理Python任务请求时发生错误: {str(e)}", exc_info=True)
            raise

    async def on_cancel_task(self, request):
        return CancelTaskResponse(id=request.id, result={"status": "cancelled", "message": "任务已取消"})

    async def on_get_task_push_notification(self, request):
        return GetTaskResponse(id=request.id, result={"enabled": False})

    async def on_set_task_push_notification(self, request):
        return SendTaskResponse(id=request.id, result={"enabled": True})

    async def on_send_task_subscribe(self, request):
        async def generate_responses():
            yield SendTaskResponse(id=request.id, result=Task(
                id=request.params.id,
                sessionId=request.params.sessionId,
                status=TaskStatus(state=TaskState.WORKING),
                history=[Message(role="agent", parts=[{"type": "text", "text": "正在生成Python代码..."}])]
            ))
            yield SendTaskResponse(id=request.id, result=Task(
                id=request.params.id,
                sessionId=request.params.sessionId,
                status=TaskStatus(state=TaskState.COMPLETED),
                history=[Message(role="agent", parts=[{"type": "text", "text": "代码生成完成"}])]
            ))
        return generate_responses()

    async def on_resubscribe_to_task(self, request):
        async def generate_responses():
            yield SendTaskResponse(id=request.id, result={"status": "resubscribed", "message": "任务已重新订阅"})
        return generate_responses()

def main():
    # 创建 Agent 卡片
    agent_card = AgentCard(
        name="Python代码生成器",
        description="一个基于DeepSeek平台的Python代码生成助手，帮助您编写高质量的Python代码",
        version="1.0.0",
        url="http://localhost:5000",
        capabilities={
            "text": True,
            "image": False,
            "audio": False,
            "video": False
        },
        skills=[
            AgentSkill(
                id="python_code_generation",
                name="Python代码生成",
                description="基于DeepSeek平台生成高质量的Python代码"
            )
        ]
    )

    # 创建服务器
    server = A2AServer(
        host="0.0.0.0",
        port=5000,
        agent_card=agent_card,
        task_manager=PythonCodeAgent()
    )

    # 启动服务器
    server.start()

if __name__ == "__main__":
    main() 