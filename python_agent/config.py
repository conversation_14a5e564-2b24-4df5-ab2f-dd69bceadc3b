import os
from pathlib import Path
import json

class Config:
    def __init__(self):
        self.config_path = Path(__file__).parent / "config.json"
        self.config = self._load_config()
        
    def _load_config(self):
        if not self.config_path.exists():
            # 创建默认配置
            default_config = {
                "deepseek": {
                    "api_key": "",
                    "api_base": "https://api.deepseek.com/v1",
                    "model": "deepseek-chat"
                }
            }
            # 保存默认配置
            with open(self.config_path, "w", encoding="utf-8") as f:
                json.dump(default_config, f, indent=4, ensure_ascii=False)
            return default_config
        
        # 读取现有配置
        with open(self.config_path, "r", encoding="utf-8") as f:
            return json.load(f)
    
    def get_deepseek_config(self):
        return self.config.get("deepseek", {})
    
    def update_deepseek_api_key(self, api_key: str):
        self.config["deepseek"]["api_key"] = api_key
        with open(self.config_path, "w", encoding="utf-8") as f:
            json.dump(self.config, f, indent=4, ensure_ascii=False)

# 创建全局配置实例
config = Config() 