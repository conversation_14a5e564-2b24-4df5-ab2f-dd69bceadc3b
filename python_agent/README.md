# Python代码生成器

这是一个基于DeepSeek平台的Python代码生成助手，可以帮助您编写高质量的Python代码。

## 安装

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 配置DeepSeek API密钥：
   - 在 `python_agent` 目录下找到 `config.json` 文件
   - 如果没有该文件，运行程序时会自动创建
   - 在 `config.json` 中设置您的 DeepSeek API 密钥：
```json
{
    "deepseek": {
        "api_key": "您的API密钥",
        "api_base": "https://api.deepseek.com/v1",
        "model": "deepseek-chat"
    }
}
```

## 运行

```bash
python run_python_agent.py
```

服务器将在 http://localhost:5000 启动。

## 使用

1. 服务器启动后，可以通过 HTTP 请求与代码生成器交互
2. 发送 POST 请求到 http://localhost:5000/ 端点
3. 请求格式示例：
```json
{
    "jsonrpc": "2.0",
    "method": "tasks/send",
    "params": {
        "id": "test-task-1",
        "sessionId": "test-session-1",
        "message": {
            "role": "user",
            "parts": [
                {
                    "type": "text",
                    "text": "请帮我写一个Python函数来计算斐波那契数列"
                }
            ]
        }
    },
    "id": 1
}
```

## 功能特点

- 使用DeepSeek平台的最新模型
- 支持中文交互
- 生成符合PEP 8规范的代码
- 提供详细的代码注释和文档
- 考虑代码的可维护性和可扩展性
- 实现错误处理和日志记录 