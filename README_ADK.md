# JoyAgent A2A - ADK 版本

基于 Google Agent Development Kit (ADK) 重构的智能代码生成代理系统。

## 🚀 新特性

### ADK 框架优势
- **标准化架构**: 使用 Google 官方的代理开发框架
- **简化开发**: 减少 80% 的样板代码
- **内置功能**: 会话管理、状态管理、工具集成
- **多种运行模式**: Web UI、终端交互、API 服务器
- **更好的调试**: 内置调试工具和可视化界面
- **扩展性**: 支持多代理系统和复杂工作流

### 代理能力
- **Python 代理**: 生成高质量的 Python 代码
- **前端代理**: 生成现代前端代码 (React/Vue/Angular)
- **智能分析**: 自动分析需求类型并推荐合适的代理
- **流式响应**: 实时显示代码生成过程

## 📦 安装

### 1. 克隆项目
```bash
git clone <repository-url>
cd joyagent-a2a
```

### 2. 创建虚拟环境
```bash
python -m venv .venv
source .venv/bin/activate  # Linux/Mac
# 或
.venv\Scripts\activate     # Windows
```

### 3. 安装依赖
```bash
pip install -r requirements.txt
```

## ⚙️ 配置

### 1. API 密钥配置

编辑 `.env` 文件，配置 API 密钥：

```bash
# Gemini API (推荐 - Google AI Studio)
GOOGLE_GENAI_USE_VERTEXAI=FALSE
GOOGLE_API_KEY=your_gemini_api_key_here

# DeepSeek API (后备选项)
DEEPSEEK_API_KEY=your_deepseek_api_key_here
```

### 2. 获取 API 密钥

#### Gemini API (推荐)
1. 访问 [Google AI Studio](https://aistudio.google.com/apikey)
2. 创建 API 密钥
3. 将密钥添加到 `.env` 文件

#### DeepSeek API (后备)
1. 访问 [DeepSeek 平台](https://platform.deepseek.com/)
2. 获取 API 密钥
3. 将密钥添加到 `.env` 文件

## 🎯 使用方法

### 1. Web UI 模式 (推荐)

启动可视化界面：
```bash
python main.py web
```

然后在浏览器中访问: http://localhost:8000

特性：
- 🎨 直观的聊天界面
- 🔍 实时事件监控
- 📊 性能追踪
- 🎤 语音交互支持

### 2. 终端交互模式

#### Python 代理
```bash
python main.py terminal --agent python
```

#### 前端代理
```bash
python main.py terminal --agent frontend
```

### 3. API 服务器模式

启动 REST API 服务：
```bash
python main.py api
```

API 文档: http://localhost:8000/docs

## 💡 使用示例

### Python 代码生成
```
用户: 请帮我写一个计算斐波那契数列的函数
代理: 我来为您生成一个高效的斐波那契数列计算函数...
```

### 前端代码生成
```
用户: 创建一个 React 登录组件
代理: 我将为您创建一个现代化的 React 登录组件...
```

### 需求分析
```
用户: 我需要一个用户管理系统
代理: 根据您的需求分析，建议使用前端代理创建界面，Python代理创建后端API...
```

## 🏗️ 项目结构

```
joyagent-a2a/
├── adk_agents/              # ADK 代理模块
│   ├── python_agent.py     # Python 代码生成代理
│   ├── frontend_agent.py   # 前端代码生成代理
│   └── tools/               # 工具函数
│       ├── code_generation.py
│       └── deepseek_client.py
├── config/                  # 配置管理
│   └── settings.py
├── .env                     # 环境变量
├── main.py                  # 主启动文件
└── requirements.txt         # 依赖列表
```

## 🔧 高级配置

### 自定义模型
在 `config/settings.py` 中修改模型配置：
```python
"gemini": {
    "model": "gemini-2.0-flash"  # 或其他支持的模型
}
```

### 代理端口
在 `.env` 文件中配置：
```bash
PYTHON_AGENT_PORT=8001
FRONTEND_AGENT_PORT=8002
```

## 🆚 与原版本对比

| 特性 | 原版本 (A2A) | ADK 版本 |
|------|-------------|----------|
| 框架 | 自定义 A2A 协议 | Google ADK |
| 代码量 | ~1000 行 | ~300 行 |
| 调试工具 | 基础日志 | 可视化界面 |
| 会话管理 | 手动实现 | 内置支持 |
| 扩展性 | 有限 | 高度可扩展 |
| 维护成本 | 高 | 低 |

## 🐛 故障排除

### 常见问题

1. **API 密钥错误**
   - 检查 `.env` 文件中的密钥是否正确
   - 确认密钥有足够的权限

2. **模块导入错误**
   - 确保在项目根目录运行
   - 检查虚拟环境是否激活

3. **端口占用**
   - 修改 `.env` 文件中的端口配置
   - 或终止占用端口的进程

### 日志查看
```bash
# 设置详细日志
export LOG_LEVEL=DEBUG
python main.py web
```

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
