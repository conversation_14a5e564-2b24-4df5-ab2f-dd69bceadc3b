#!/usr/bin/env python3
"""
ADK 代理功能测试脚本
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from adk_agents.tools import generate_python_code, generate_frontend_code, analyze_code_requirements


async def test_python_code_generation():
    """测试 Python 代码生成"""
    print("🐍 测试 Python 代码生成...")
    
    requirement = "请写一个计算斐波那契数列的函数，支持递归和迭代两种方式"
    
    try:
        result = generate_python_code(requirement)
        
        if result["status"] == "success":
            print("✅ Python 代码生成成功!")
            print(f"生成的代码:\n{result['code'][:200]}...")
        else:
            print(f"❌ Python 代码生成失败: {result.get('error', '未知错误')}")
            
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {str(e)}")


async def test_frontend_code_generation():
    """测试前端代码生成"""
    print("\n🎨 测试前端代码生成...")
    
    requirement = "创建一个 React 登录组件，包含用户名和密码输入框，以及登录按钮"
    
    try:
        result = generate_frontend_code(requirement)
        
        if result["status"] == "success":
            print("✅ 前端代码生成成功!")
            print(f"生成的代码:\n{result['code'][:200]}...")
        else:
            print(f"❌ 前端代码生成失败: {result.get('error', '未知错误')}")
            
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {str(e)}")


def test_requirement_analysis():
    """测试需求分析"""
    print("\n🔍 测试需求分析...")
    
    test_cases = [
        "我需要一个 Python 爬虫来抓取网页数据",
        "创建一个 Vue.js 的用户管理界面",
        "写一个通用的数据处理函数"
    ]
    
    for requirement in test_cases:
        try:
            result = analyze_code_requirements(requirement)
            print(f"需求: {requirement}")
            print(f"分析结果: {result['analysis']}")
            print(f"建议类型: {result['suggested_type']}, 置信度: {result['confidence']:.2f}")
            print("-" * 50)
            
        except Exception as e:
            print(f"❌ 分析需求时出现异常: {str(e)}")


def test_agent_import():
    """测试代理导入"""
    print("📦 测试代理导入...")
    
    try:
        from adk_agents import python_agent, frontend_agent
        print(f"✅ 代理导入成功!")
        print(f"Python 代理: {python_agent.name}")
        print(f"前端代理: {frontend_agent.name}")
        return True
    except Exception as e:
        print(f"❌ 代理导入失败: {str(e)}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始 ADK 代理功能测试\n")
    
    # 测试代理导入
    if not test_agent_import():
        print("❌ 代理导入失败，停止测试")
        return
    
    print("\n" + "="*60)
    
    # 测试需求分析
    test_requirement_analysis()
    
    print("\n" + "="*60)
    
    # 测试代码生成功能
    await test_python_code_generation()
    await test_frontend_code_generation()
    
    print("\n🎉 测试完成!")
    print("\n💡 提示:")
    print("- 如果代码生成失败，请检查 DeepSeek API 密钥配置")
    print("- 可以通过 'python main.py web' 启动 Web UI 进行交互测试")
    print("- 可以通过 'python main.py terminal --agent python' 启动终端模式")


if __name__ == "__main__":
    asyncio.run(main())
