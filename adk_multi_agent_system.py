#!/usr/bin/env python3
"""
基于 ADK 框架的真正多智能体系统
使用 ADK 的官方多智能体架构
"""
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from google.adk.agents import LlmAgent, SequentialAgent, ParallelAgent
from google.adk.tools import AgentTool
from adk_agents.tools import generate_python_code, generate_frontend_code


def create_adk_multi_agent_system():
    """创建基于 ADK 的多智能体系统"""
    
    # 1. 创建专业化的子代理
    python_specialist = LlmAgent(
        name="PythonSpecialist",
        model="gemini-2.0-flash",
        description="专门处理Python后端开发、API设计、数据处理和算法实现的专家代理",
        instruction="""你是一个专业的Python开发专家。你的职责是：
1. 生成高质量的Python代码
2. 设计RESTful API
3. 实现数据处理逻辑
4. 编写算法和数据结构
5. 确保代码符合PEP 8规范
请用中文回复，并提供详细的代码注释。""",
        tools=[generate_python_code]
    )
    
    frontend_specialist = LlmAgent(
        name="FrontendSpecialist", 
        model="gemini-2.0-flash",
        description="专门处理前端界面、React/Vue组件、用户交互和响应式设计的专家代理",
        instruction="""你是一个专业的前端开发专家。你的职责是：
1. 创建现代化的前端界面
2. 开发React/Vue/Angular组件
3. 实现响应式设计
4. 优化用户体验
5. 编写TypeScript/JavaScript代码
请用中文回复，并提供清晰的实现思路。""",
        tools=[generate_frontend_code]
    )
    
    # 2. 创建架构师代理（负责系统设计）
    system_architect = LlmAgent(
        name="SystemArchitect",
        model="gemini-2.0-flash", 
        description="负责系统架构设计、技术选型和项目规划的架构师代理",
        instruction="""你是一个系统架构师。你的职责是：
1. 分析用户需求
2. 设计系统架构
3. 制定技术方案
4. 规划开发流程
5. 协调前后端开发
当需要具体实现时，请将任务委托给相应的专家代理。""",
        sub_agents=[python_specialist, frontend_specialist]
    )
    
    # 3. 创建全栈协作工作流
    fullstack_workflow = SequentialAgent(
        name="FullStackWorkflow",
        sub_agents=[
            system_architect,  # 先进行架构设计
            ParallelAgent(     # 然后并行开发前后端
                name="ParallelDevelopment",
                sub_agents=[python_specialist, frontend_specialist]
            )
        ]
    )
    
    # 4. 创建主协调器代理
    main_coordinator = LlmAgent(
        name="MainCoordinator",
        model="gemini-2.0-flash",
        description="主协调器，负责理解用户需求并协调各个专家代理完成任务",
        instruction="""你是一个智能项目协调器。你的职责是：

1. **需求分析**：理解用户的需求类型
2. **任务路由**：根据需求选择合适的处理方式
3. **代理协调**：协调各个专家代理的工作

**处理规则**：
- 简单的Python需求 → 直接转给 PythonSpecialist
- 简单的前端需求 → 直接转给 FrontendSpecialist  
- 复杂的系统需求 → 转给 SystemArchitect 进行架构设计
- 全栈项目需求 → 启动 FullStackWorkflow 进行协作开发

**转移语法**：
- 使用 transfer_to_agent(agent_name='代理名称') 来转移任务
- 可选的代理：PythonSpecialist, FrontendSpecialist, SystemArchitect

请用中文与用户交流，并根据需求智能选择处理方式。""",
        sub_agents=[
            python_specialist,
            frontend_specialist, 
            system_architect,
            fullstack_workflow
        ]
    )
    
    return main_coordinator


def create_adk_agent_tools_system():
    """创建基于 AgentTool 的多智能体系统"""
    
    # 创建工具化的代理
    python_agent_tool = AgentTool(
        agent=LlmAgent(
            name="PythonTool",
            model="gemini-2.0-flash",
            description="Python代码生成工具",
            instruction="生成高质量的Python代码",
            tools=[generate_python_code]
        )
    )
    
    frontend_agent_tool = AgentTool(
        agent=LlmAgent(
            name="FrontendTool", 
            model="gemini-2.0-flash",
            description="前端代码生成工具",
            instruction="生成现代化的前端代码",
            tools=[generate_frontend_code]
        )
    )
    
    # 创建使用工具的主代理
    tool_coordinator = LlmAgent(
        name="ToolCoordinator",
        model="gemini-2.0-flash",
        description="使用代理工具的协调器",
        instruction="""你是一个代码生成协调器。你可以使用以下工具：

1. **PythonTool**: 生成Python代码
2. **FrontendTool**: 生成前端代码

根据用户需求，选择合适的工具来完成任务。可以同时使用多个工具来完成复杂的全栈需求。

请用中文与用户交流。""",
        tools=[python_agent_tool, frontend_agent_tool]
    )
    
    return tool_coordinator


# 导出代理实例
adk_coordinator = create_adk_multi_agent_system()
adk_tool_coordinator = create_adk_agent_tools_system()

if __name__ == "__main__":
    print("🤖 ADK 多智能体系统已创建")
    print(f"主协调器: {adk_coordinator.name}")
    print(f"子代理数量: {len(adk_coordinator.sub_agents)}")
    print(f"工具协调器: {adk_tool_coordinator.name}")
    print(f"可用工具: {len(adk_tool_coordinator.tools)}")
