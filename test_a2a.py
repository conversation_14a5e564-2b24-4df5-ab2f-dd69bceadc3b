import asyncio
import httpx
import json
import logging
from typing import Dict, Any
from client.client_agent import ClientAgent

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_a2a_communication():
    """测试A2A协议实现"""
    # 测试数据
    test_request = {
        "description": "创建一个CRM系统，包含用户管理、客户管理、订单管理等功能。",
        "requirements": {
            "features": [
                "用户认证和授权",
                "客户信息管理",
                "订单跟踪",
                "数据统计和报表"
            ]
        }
    }
    
    # 客户端智能体地址
    client_agent_url = "http://localhost:8000"
    
    try:
        # 创建 ClientAgent 实例
        client_agent = ClientAgent()
        logger.info("ClientAgent 实例已创建")
        
        # 创建带有更长超时时间的客户端
        async with httpx.AsyncClient(timeout=60.0) as client:
            logger.info("准备发送任务请求...")
            logger.info(f"请求数据: {json.dumps(test_request, ensure_ascii=False, indent=2)}")
            
            # 首先检查 Client Agent 是否在线
            try:
                health_check = await client.get(f"{client_agent_url}/docs", timeout=5.0)
                if health_check.status_code != 200:
                    logger.error("Client Agent 不可用")
                    return
                logger.info("Client Agent 在线")
            except Exception as e:
                logger.error(f"Client Agent 健康检查失败: {str(e)}")
                return
            
            # 发送任务请求
            try:
                async with client.stream(
                    "POST",
                    f"{client_agent_url}/task",
                    json=test_request,
                    timeout=60.0
                ) as response:
                    if response.status_code != 200:
                        error_text = await response.text()
                        logger.error(f"任务请求失败: {error_text}")
                        logger.error(f"状态码: {response.status_code}")
                        return
                    
                    collected_response = ""
                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            data = line[6:]
                            if data == "[DONE]":
                                break
                            try:
                                # 解码响应数据
                                if isinstance(data, bytes):
                                    data = data.decode('utf-8')
                                chunk = json.loads(data)
                                if chunk.get("result"):
                                    result = chunk["result"]
                                    if isinstance(result, dict):
                                        # 确保中文字符正确显示
                                        collected_response = json.dumps(result, ensure_ascii=False)
                                        # 打印进度信息
                                        if "status" in result:
                                            status_text = json.dumps(result['status'], ensure_ascii=False)
                                            logger.info(f"任务状态更新: {status_text}")
                                        # 如果是最终结果，打印完整结果
                                        if result.get("status", {}).get("state") == "completed":
                                            logger.info("任务完成！")
                                            logger.info("生成结果:")
                                            print(json.dumps(result, indent=2, ensure_ascii=False))
                                            break
                            except json.JSONDecodeError as e:
                                logger.error(f"JSON解析错误: {str(e)}, 原始数据: {data}")
                                continue
                    
                    # 如果没有收到完成状态，打印当前收集的响应
                    if collected_response:
                        logger.info("收到部分响应:")
                        print(json.dumps(json.loads(collected_response), indent=2, ensure_ascii=False))
                        
            except httpx.TimeoutException:
                logger.error("发送任务请求超时，请检查服务是否正常运行")
            except httpx.RequestError as e:
                logger.error(f"发送任务请求失败: {str(e)}")
            except Exception as e:
                logger.error(f"发送任务请求时发生错误: {str(e)}")
                
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(test_a2a_communication()) 