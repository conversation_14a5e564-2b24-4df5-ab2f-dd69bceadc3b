#!/usr/bin/env python3
"""
ADK 代理终端交互脚本
提供简单的命令行聊天界面
"""
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from adk_agents import python_agent, frontend_agent
from adk_agents.tools import generate_python_code, generate_frontend_code, analyze_code_requirements


async def chat_with_agent(agent_type="python"):
    """与代理进行聊天交互"""
    
    if agent_type == "python":
        agent = python_agent
        generate_func = generate_python_code
        print(f"🐍 欢迎使用 {agent.name}!")
    elif agent_type == "frontend":
        agent = frontend_agent
        generate_func = generate_frontend_code
        print(f"🎨 欢迎使用 {agent.name}!")
    else:
        print("❌ 未知的代理类型")
        return
    
    print(f"描述: {agent.description}")
    print("输入 'quit' 或 'exit' 退出，输入 'help' 查看帮助")
    print("-" * 60)
    
    while True:
        try:
            user_input = input("\n您: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 再见！")
                break
            
            if user_input.lower() in ['help', '帮助']:
                print_help(agent_type)
                continue
            
            if not user_input:
                continue
            
            print(f"\n{agent.name}: 正在处理您的请求...")
            
            # 分析需求
            analysis = analyze_code_requirements(user_input)
            if analysis['suggested_type'] != agent_type and analysis['confidence'] > 0.7:
                print(f"💡 建议: {analysis['analysis']}")
                response = input("是否继续使用当前代理？(y/n): ").strip().lower()
                if response in ['n', 'no', '否']:
                    print("请使用建议的代理类型")
                    continue
            
            # 生成代码
            result = await generate_func(user_input)
            
            if result['status'] == 'success':
                print(f"\n✅ {result['message']}")
                print(f"\n生成的代码:\n{'-'*40}")
                print(result['code'])
                print("-" * 40)
            else:
                print(f"\n❌ {result['message']}")
                if 'error' in result:
                    print(f"错误详情: {result['error']}")
                    
        except KeyboardInterrupt:
            print("\n\n👋 再见！")
            break
        except Exception as e:
            print(f"\n❌ 发生错误: {str(e)}")


def print_help(agent_type):
    """打印帮助信息"""
    print("\n📖 使用帮助:")
    print("=" * 40)
    
    if agent_type == "python":
        print("🐍 Python 代理可以帮您:")
        print("• 生成 Python 函数和类")
        print("• 编写数据处理脚本")
        print("• 创建 API 接口")
        print("• 实现算法和数据结构")
        print("• 编写爬虫和自动化脚本")
        print("\n示例:")
        print("- 写一个计算斐波那契数列的函数")
        print("- 创建一个简单的 Flask API")
        print("- 实现快速排序算法")
    
    elif agent_type == "frontend":
        print("🎨 前端代理可以帮您:")
        print("• 创建 React/Vue/Angular 组件")
        print("• 编写 HTML/CSS/JavaScript")
        print("• 实现响应式设计")
        print("• 创建交互式界面")
        print("• 编写 TypeScript 代码")
        print("\n示例:")
        print("- 创建一个 React 登录组件")
        print("- 写一个响应式导航栏")
        print("- 实现一个 Vue.js 数据表格")
    
    print("\n命令:")
    print("• help/帮助 - 显示此帮助")
    print("• quit/exit/退出 - 退出程序")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="ADK 代理终端交互")
    parser.add_argument(
        "--agent",
        choices=["python", "frontend"],
        default="python",
        help="选择代理类型"
    )
    
    args = parser.parse_args()
    
    print("🚀 ADK 代理终端交互")
    print("=" * 30)
    
    try:
        asyncio.run(chat_with_agent(args.agent))
    except Exception as e:
        print(f"❌ 启动失败: {str(e)}")


if __name__ == "__main__":
    main()
