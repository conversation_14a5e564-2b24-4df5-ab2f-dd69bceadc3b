#!/usr/bin/env python3
"""
ADK 代理主启动文件
支持多种运行模式：Web UI、终端交互、API 服务器
"""
import os
import sys
import argparse
import logging
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from adk_agents import python_agent, frontend_agent

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    encoding='utf-8'
)
logger = logging.getLogger(__name__)


def run_web_ui():
    """启动 Web UI 模式"""
    logger.info("启动 ADK Web UI...")
    logger.info("请在浏览器中访问: http://localhost:8000")
    logger.info("可用的代理:")
    logger.info("- python_agent: Python代码生成器")
    logger.info("- frontend_agent: 前端代码生成器")

    # 使用 ADK 的 web 命令
    os.system("adk web")


def run_terminal_mode(agent_name: str):
    """启动终端交互模式"""
    if agent_name == "python":
        logger.info("启动 Python 代理终端模式...")
        os.system("python terminal_chat.py --agent python")
    elif agent_name == "frontend":
        logger.info("启动前端代理终端模式...")
        os.system("python terminal_chat.py --agent frontend")
    else:
        logger.error(f"未知的代理名称: {agent_name}")
        logger.info("可用的代理: python, frontend")


def run_multi_agent_mode():
    """启动多智能体协作模式"""
    logger.info("启动 ADK 多智能体协作模式...")
    os.system("python adk_multi_agent_terminal.py")


def run_api_server():
    """启动 API 服务器模式"""
    logger.info("启动 ADK API 服务器...")
    logger.info("API 文档: http://localhost:8000/docs")

    # 使用 ADK 的 api_server 命令
    os.system("adk api_server")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="ADK 代理启动器")
    parser.add_argument(
        "mode",
        choices=["web", "terminal", "api", "multi"],
        help="运行模式: web (Web UI), terminal (终端交互), api (API服务器), multi (多智能体协作)"
    )
    parser.add_argument(
        "--agent",
        choices=["python", "frontend"],
        help="指定代理 (仅在 terminal 模式下使用)"
    )

    args = parser.parse_args()

    # 检查环境配置
    if not os.path.exists(".env"):
        logger.warning("未找到 .env 文件，请确保已正确配置环境变量")

    try:
        if args.mode == "web":
            run_web_ui()
        elif args.mode == "terminal":
            if not args.agent:
                logger.error("终端模式需要指定 --agent 参数")
                parser.print_help()
                return
            run_terminal_mode(args.agent)
        elif args.mode == "api":
            run_api_server()
        elif args.mode == "multi":
            run_multi_agent_mode()
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序运行出错: {str(e)}")


if __name__ == "__main__":
    main()
