#!/usr/bin/env python3
"""
ADK 代理演示脚本
展示迁移后的功能
"""
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from adk_agents.tools import generate_python_code, generate_frontend_code, analyze_code_requirements


async def demo_python_generation():
    """演示 Python 代码生成"""
    print("🐍 Python 代码生成演示")
    print("=" * 40)
    
    requirement = "写一个计算斐波那契数列的函数，支持递归和迭代两种方式"
    print(f"需求: {requirement}")
    print("\n正在生成代码...")
    
    try:
        result = await generate_python_code(requirement)
        
        if result['status'] == 'success':
            print("✅ 生成成功!")
            print(f"\n{result['code'][:500]}...")
            if len(result['code']) > 500:
                print("\n[代码已截断，完整版本请使用 Web UI 查看]")
        else:
            print(f"❌ 生成失败: {result.get('error', '未知错误')}")
    except Exception as e:
        print(f"❌ 演示过程中出现异常: {str(e)}")


async def demo_frontend_generation():
    """演示前端代码生成"""
    print("\n🎨 前端代码生成演示")
    print("=" * 40)
    
    requirement = "创建一个 React 登录组件，包含用户名和密码输入框"
    print(f"需求: {requirement}")
    print("\n正在生成代码...")
    
    try:
        result = await generate_frontend_code(requirement)
        
        if result['status'] == 'success':
            print("✅ 生成成功!")
            print(f"\n{result['code'][:500]}...")
            if len(result['code']) > 500:
                print("\n[代码已截断，完整版本请使用 Web UI 查看]")
        else:
            print(f"❌ 生成失败: {result.get('error', '未知错误')}")
    except Exception as e:
        print(f"❌ 演示过程中出现异常: {str(e)}")


def demo_requirement_analysis():
    """演示需求分析"""
    print("\n🔍 智能需求分析演示")
    print("=" * 40)
    
    test_cases = [
        "我需要一个 Python 爬虫",
        "创建一个 Vue.js 组件",
        "写一个数据处理函数"
    ]
    
    for requirement in test_cases:
        result = analyze_code_requirements(requirement)
        print(f"需求: {requirement}")
        print(f"建议: {result['suggested_type']} 代理 (置信度: {result['confidence']:.2f})")
        print("-" * 30)


def demo_agent_info():
    """演示代理信息"""
    print("📦 ADK 代理信息")
    print("=" * 40)
    
    try:
        from adk_agents import python_agent, frontend_agent
        
        print(f"Python 代理:")
        print(f"  名称: {python_agent.name}")
        print(f"  描述: {python_agent.description}")
        print(f"  工具数量: {len(python_agent.tools)}")
        
        print(f"\n前端代理:")
        print(f"  名称: {frontend_agent.name}")
        print(f"  描述: {frontend_agent.description}")
        print(f"  工具数量: {len(frontend_agent.tools)}")
        
    except Exception as e:
        print(f"❌ 获取代理信息失败: {str(e)}")


async def main():
    """主演示函数"""
    print("🚀 ADK 代理功能演示")
    print("=" * 50)
    print("这是一个快速演示，展示迁移到 ADK 后的功能")
    print("=" * 50)
    
    # 代理信息
    demo_agent_info()
    
    print("\n" + "=" * 50)
    
    # 需求分析
    demo_requirement_analysis()
    
    print("\n" + "=" * 50)
    
    # 代码生成演示
    await demo_python_generation()
    await demo_frontend_generation()
    
    print("\n" + "=" * 50)
    print("🎉 演示完成!")
    print("\n💡 下一步:")
    print("1. 启动 Web UI: python main.py web")
    print("2. 启动终端模式: python main.py terminal --agent python")
    print("3. 查看完整文档: README_ADK.md")


if __name__ == "__main__":
    asyncio.run(main())
