#!/bin/bash

# ADK 代理快速启动脚本

echo "🚀 JoyAgent A2A - ADK 版本"
echo "=========================="

# 检查虚拟环境
if [[ "$VIRTUAL_ENV" == "" ]]; then
    echo "⚠️  建议激活虚拟环境:"
    echo "   source .venv/bin/activate"
    echo ""
fi

# 检查依赖
echo "📦 检查依赖..."
python -c "import google.adk" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ ADK 未安装，正在安装..."
    pip install -r requirements.txt
fi

# 运行测试
echo "🧪 运行基础测试..."
python simple_test.py

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 测试通过！选择启动模式:"
    echo ""
    echo "1) Web UI (推荐)"
    echo "2) Python 代理终端模式"
    echo "3) 前端代理终端模式"
    echo "4) API 服务器"
    echo "5) 退出"
    echo ""
    read -p "请选择 (1-5): " choice
    
    case $choice in
        1)
            echo "🌐 启动 Web UI..."
            echo "访问: http://localhost:8000"
            python main.py web
            ;;
        2)
            echo "🐍 启动 Python 代理终端模式..."
            python main.py terminal --agent python
            ;;
        3)
            echo "🎨 启动前端代理终端模式..."
            python main.py terminal --agent frontend
            ;;
        4)
            echo "🔌 启动 API 服务器..."
            echo "API 文档: http://localhost:8000/docs"
            python main.py api
            ;;
        5)
            echo "👋 再见！"
            exit 0
            ;;
        *)
            echo "❌ 无效选择"
            exit 1
            ;;
    esac
else
    echo "❌ 测试失败，请检查配置"
    exit 1
fi
