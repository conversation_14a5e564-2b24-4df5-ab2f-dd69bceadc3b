"""
DeepSeek API 客户端工具
为 ADK 代理提供 DeepSeek API 调用功能
"""
import httpx
import json
import logging
from typing import AsyncGenerator, Dict, Any, Optional
from config import settings

logger = logging.getLogger(__name__)


class DeepSeekClient:
    """DeepSeek API 客户端"""
    
    def __init__(self):
        self.config = settings.get_deepseek_config()
        self.api_key = settings.get_api_key("deepseek")
        if not self.api_key:
            raise ValueError("请设置 DeepSeek API 密钥")
        
        self.api_base = self.config.get("api_base", "https://api.deepseek.com/v1")
        self.model = self.config.get("model", "deepseek-chat")
        
        logger.info(f"初始化 DeepSeek 客户端: {self.api_base}, 模型: {self.model}")
    
    async def generate_code(
        self, 
        user_prompt: str, 
        system_prompt: str,
        temperature: float = 0.7,
        max_tokens: int = 2048
    ) -> AsyncGenerator[str, None]:
        """
        生成代码的流式响应
        
        Args:
            user_prompt: 用户提示
            system_prompt: 系统提示
            temperature: 温度参数
            max_tokens: 最大令牌数
            
        Yields:
            str: 生成的代码片段
        """
        async with httpx.AsyncClient(timeout=60.0) as client:
            try:
                async with client.stream(
                    "POST",
                    f"{self.api_base}/chat/completions",
                    headers={
                        "Authorization": f"Bearer {self.api_key}",
                        "Content-Type": "application/json"
                    },
                    json={
                        "model": self.model,
                        "messages": [
                            {"role": "system", "content": system_prompt},
                            {"role": "user", "content": user_prompt}
                        ],
                        "temperature": temperature,
                        "max_tokens": max_tokens,
                        "stream": True
                    }
                ) as response:
                    if response.status_code != 200:
                        error_msg = f"DeepSeek API 请求失败: {await response.text()}"
                        logger.error(error_msg)
                        raise Exception(error_msg)
                    
                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            data = line[6:]  # 移除 "data: " 前缀
                            if data.strip() == "[DONE]":
                                break
                            
                            try:
                                json_data = json.loads(data)
                                choices = json_data.get("choices", [])
                                if choices:
                                    delta = choices[0].get("delta", {})
                                    content = delta.get("content", "")
                                    if content:
                                        yield content
                            except json.JSONDecodeError:
                                continue
                                
            except httpx.TimeoutException:
                error_msg = "DeepSeek API 请求超时"
                logger.error(error_msg)
                raise Exception(error_msg)
            except httpx.RequestError as e:
                error_msg = f"DeepSeek API 请求错误: {str(e)}"
                logger.error(error_msg)
                raise Exception(error_msg)
    
    async def generate_code_sync(
        self, 
        user_prompt: str, 
        system_prompt: str,
        temperature: float = 0.7,
        max_tokens: int = 2048
    ) -> str:
        """
        生成代码的同步响应
        
        Args:
            user_prompt: 用户提示
            system_prompt: 系统提示
            temperature: 温度参数
            max_tokens: 最大令牌数
            
        Returns:
            str: 生成的完整代码
        """
        result = ""
        async for chunk in self.generate_code(user_prompt, system_prompt, temperature, max_tokens):
            result += chunk
        return result


# 全局客户端实例
deepseek_client = DeepSeekClient()
