"""
代码生成工具函数
为 ADK 代理提供代码生成功能
"""
import asyncio
import logging
from typing import Dict, Any
from .deepseek_client import deepseek_client

logger = logging.getLogger(__name__)


async def generate_python_code(requirement: str) -> Dict[str, Any]:
    """
    生成 Python 代码

    Args:
        requirement (str): 用户的代码需求描述

    Returns:
        dict: 包含生成的代码和状态信息
    """
    system_prompt = """你是一个专业的Python后端开发专家。你的任务是：
1. 根据用户需求生成高质量的Python代码
2. 确保代码符合PEP 8规范
3. 提供必要的注释和文档
4. 考虑代码的可维护性和可扩展性
5. 实现错误处理和日志记录
请用中文回复，并在代码前说明实现思路。"""

    try:
        code = await deepseek_client.generate_code_sync(requirement, system_prompt)

        return {
            "status": "success",
            "code": code,
            "language": "python",
            "message": "Python代码生成成功"
        }

    except Exception as e:
        logger.error(f"Python代码生成失败: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "message": "Python代码生成失败"
        }


async def generate_frontend_code(requirement: str) -> Dict[str, Any]:
    """
    生成前端代码

    Args:
        requirement (str): 用户的前端代码需求描述

    Returns:
        dict: 包含生成的代码和状态信息
    """
    system_prompt = """你是一个专业的前端开发专家，精通现代前端技术栈。你的任务是：
1. 根据用户需求生成高质量的前端代码，包括但不限于：
   - React/Vue/Angular等现代框架
   - TypeScript/JavaScript
   - HTML5/CSS3
   - 响应式设计
   - 组件化开发
2. 确保代码符合以下标准：
   - 遵循最佳实践和设计模式
   - 使用最新的ES6+特性
   - 考虑性能优化
   - 实现优雅的错误处理
   - 添加必要的类型检查（TypeScript）
3. 提供清晰的代码注释和文档说明
4. 注重代码的：
   - 可维护性
   - 可扩展性
   - 可测试性
   - 可重用性
5. 考虑用户体验和界面设计
6. 实现必要的错误处理和日志记录

请用中文回复，并在代码前详细说明实现思路和技术选型。"""

    try:
        code = await deepseek_client.generate_code_sync(requirement, system_prompt)

        return {
            "status": "success",
            "code": code,
            "language": "frontend",
            "message": "前端代码生成成功"
        }

    except Exception as e:
        logger.error(f"前端代码生成失败: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "message": "前端代码生成失败"
        }


def analyze_code_requirements(requirement: str) -> Dict[str, Any]:
    """
    分析代码需求

    Args:
        requirement (str): 用户的需求描述

    Returns:
        dict: 需求分析结果
    """
    # 简单的关键词分析来判断代码类型
    frontend_keywords = [
        "react", "vue", "angular", "html", "css", "javascript", "typescript",
        "前端", "界面", "页面", "组件", "响应式", "ui", "ux"
    ]

    python_keywords = [
        "python", "django", "flask", "fastapi", "后端", "api", "数据库",
        "爬虫", "机器学习", "数据分析", "算法"
    ]

    requirement_lower = requirement.lower()

    frontend_score = sum(1 for keyword in frontend_keywords if keyword in requirement_lower)
    python_score = sum(1 for keyword in python_keywords if keyword in requirement_lower)

    if frontend_score > python_score:
        suggested_type = "frontend"
        confidence = frontend_score / (frontend_score + python_score) if (frontend_score + python_score) > 0 else 0.5
    elif python_score > frontend_score:
        suggested_type = "python"
        confidence = python_score / (frontend_score + python_score) if (frontend_score + python_score) > 0 else 0.5
    else:
        suggested_type = "general"
        confidence = 0.5

    return {
        "status": "success",
        "suggested_type": suggested_type,
        "confidence": confidence,
        "frontend_score": frontend_score,
        "python_score": python_score,
        "analysis": f"根据需求分析，建议使用 {suggested_type} 代理，置信度: {confidence:.2f}"
    }
