"""
基于 ADK 框架的前端代码生成代理
"""
import logging
from google.adk.agents import Agent
from .tools import generate_frontend_code, analyze_code_requirements
from config import settings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    encoding='utf-8'
)
logger = logging.getLogger(__name__)


def create_frontend_agent() -> Agent:
    """创建前端代码生成代理"""

    # 获取代理配置
    agent_config = settings.get_agent_config("frontend_agent")

    # 检查是否有 Gemini API 密钥
    gemini_api_key = settings.get_api_key("gemini")
    if not gemini_api_key:
        logger.warning("未找到 Gemini API 密钥，将使用 DeepSeek 作为后备")
        # 当没有 Gemini API 时，我们使用一个简单的配置
        # 实际的代码生成将通过 DeepSeek 工具函数完成
        model_config = "gemini-2.0-flash"  # 使用默认模型名称，但实际不会调用
    else:
        model_config = "gemini-2.0-flash"

    # 创建代理
    agent = Agent(
        name=agent_config.get("name", "前端代码生成器"),
        model=model_config,
        description=agent_config.get("description", "基于ADK框架的前端代码生成助手"),
        instruction="""你是一个专业的前端代码生成助手，精通现代前端技术栈。你可以：

1. 根据用户需求生成高质量的前端代码
2. 支持 React、Vue、Angular 等现代框架
3. 生成 TypeScript/JavaScript、HTML5/CSS3 代码
4. 实现响应式设计和组件化开发
5. 分析前端需求并提供技术建议

当用户提出前端需求时，你应该：
- 首先分析需求的类型和技术栈
- 使用 generate_frontend_code 工具生成代码
- 如果需要，使用 analyze_code_requirements 工具分析需求
- 提供清晰的实现思路和技术选型说明

你的代码应该遵循最佳实践：
- 使用最新的ES6+特性
- 考虑性能优化
- 实现优雅的错误处理
- 注重可维护性和可扩展性
- 考虑用户体验和界面设计

请用中文与用户交流，并提供清晰、专业的回复。""",
        tools=[generate_frontend_code, analyze_code_requirements]
    )

    logger.info(f"前端代理创建成功: {agent.name}")
    return agent


# 创建代理实例
frontend_agent = create_frontend_agent()
