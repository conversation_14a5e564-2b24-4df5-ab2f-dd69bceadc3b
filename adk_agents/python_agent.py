"""
基于 ADK 框架的 Python 代码生成代理
"""
import logging
from google.adk.agents import Agent
from .tools import generate_python_code, analyze_code_requirements
from config import settings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    encoding='utf-8'
)
logger = logging.getLogger(__name__)


def create_python_agent() -> Agent:
    """创建 Python 代码生成代理"""

    # 获取代理配置
    agent_config = settings.get_agent_config("python_agent")

    # 检查是否有 Gemini API 密钥
    gemini_api_key = settings.get_api_key("gemini")
    if not gemini_api_key:
        logger.warning("未找到 Gemini API 密钥，将使用 DeepSeek 作为后备")
        # 当没有 Gemini API 时，我们使用一个简单的配置
        # 实际的代码生成将通过 DeepSeek 工具函数完成
        model_config = "gemini-2.0-flash"  # 使用默认模型名称，但实际不会调用
    else:
        model_config = "gemini-2.0-flash"

    # 创建代理
    agent = Agent(
        name=agent_config.get("name", "Python代码生成器"),
        model=model_config,
        description=agent_config.get("description", "基于ADK框架的Python代码生成助手"),
        instruction="""你是一个专业的Python代码生成助手。你可以：

1. 根据用户需求生成高质量的Python代码
2. 分析代码需求并提供建议
3. 确保代码符合PEP 8规范
4. 提供详细的代码注释和文档
5. 考虑代码的可维护性和可扩展性

当用户提出代码需求时，你应该：
- 首先分析需求的类型和复杂度
- 使用 generate_python_code 工具生成代码
- 如果需要，使用 analyze_code_requirements 工具分析需求

请用中文与用户交流，并提供清晰、专业的回复。""",
        tools=[generate_python_code, analyze_code_requirements]
    )

    logger.info(f"Python代理创建成功: {agent.name}")
    return agent


# 创建代理实例
python_agent = create_python_agent()
